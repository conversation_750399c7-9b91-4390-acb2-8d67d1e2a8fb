# 支付宝当面付集成技术文档

## 📋 目录
- [系统概述](#系统概述)
- [官方SDK集成](#官方sdk集成)
- [密钥配置规范](#密钥配置规范)
- [核心API实现](#核心api实现)
- [二维码生成](#二维码生成)
- [签名验证机制](#签名验证机制)
- [异步回调处理](#异步回调处理)
- [错误处理规范](#错误处理规范)
- [安全最佳实践](#安全最佳实践)
- [测试与调试](#测试与调试)
- [生产环境配置](#生产环境配置)
- [常见问题解决](#常见问题解决)

---

## 🎯 系统概述

### 支付宝当面付简介
支付宝当面付是支付宝提供的线下扫码支付解决方案，适用于各种线下场景的收款需求。

### 核心特性
- ✅ **扫码支付** - 生成支付二维码，用户扫码完成支付
- ✅ **异步通知** - 支付结果实时回调通知
- ✅ **订单查询** - 主动查询支付状态
- ✅ **RSA2签名** - 高安全级别的数据加密
- ✅ **官方SDK** - 基于支付宝官方PHP SDK实现

### 技术要求
- **PHP版本**: 7.4+ (推荐8.0+)
- **必需扩展**: OpenSSL, cURL, JSON
- **网络要求**: 服务器能访问支付宝API
- **HTTPS**: 生产环境必须使用HTTPS

---

## 📦 官方SDK集成

### 1. SDK下载与安装

#### 官方下载地址
```
https://opendocs.alipay.com/open/54/cyz7do
```

#### 目录结构
```
vendor/
└── alipay/
    └── v2/
        └── aop/
            ├── AopClient.php                    # 核心客户端
            ├── request/
            │   ├── AlipayTradePrecreateRequest.php  # 当面付预下单
            │   └── AlipayTradeQueryRequest.php      # 订单查询
            └── sign/
                └── AlipaySignature.php         # 签名工具
```

#### 手动安装
```bash
# 下载SDK
wget https://docs.open.alipay.com/54/103419/alipay-sdk-PHP-20210625.zip

# 解压到项目目录
unzip alipay-sdk-PHP-20210625.zip
mv alipay-sdk-PHP-* vendor/alipay/v2/

# 设置权限
chmod -R 755 vendor/alipay/
```

#### Composer安装（推荐）
```bash
composer require alipay/alipay-sdk-php
```

### 2. SDK初始化

#### 标准初始化代码
```php
<?php
require_once 'vendor/alipay/v2/aop/AopClient.php';
require_once 'vendor/alipay/v2/aop/request/AlipayTradePrecreateRequest.php';
require_once 'vendor/alipay/v2/aop/request/AlipayTradeQueryRequest.php';

class AlipayClient {
    private $aopClient;
    
    public function __construct() {
        $this->initClient();
    }
    
    /**
     * 初始化支付宝客户端 - 严格按照官方规范
     */
    private function initClient() {
        $this->aopClient = new AopClient();
        
        // 设置网关地址
        $this->aopClient->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        
        // 设置应用ID
        $this->aopClient->appId = ALIPAY_APP_ID;
        
        // 设置应用私钥 (PKCS8格式)
        $this->aopClient->rsaPrivateKey = ALIPAY_PRIVATE_KEY;
        
        // 设置支付宝公钥 (用于验签)
        $this->aopClient->alipayrsaPublicKey = ALIPAY_PUBLIC_KEY;
        
        // 设置签名类型 (必须为RSA2)
        $this->aopClient->signType = 'RSA2';
        
        // 设置字符集
        $this->aopClient->postCharset = 'utf-8';
        
        // 设置数据格式
        $this->aopClient->format = 'json';
        
        // 设置API版本
        $this->aopClient->apiVersion = '1.0';
    }
    
    public function getClient() {
        return $this->aopClient;
    }
}
```

---

## 🔐 密钥配置规范

### 1. 应用创建流程

#### 支付宝开放平台操作步骤
1. 登录 [支付宝开放平台](https://open.alipay.com/)
2. 创建应用 → 选择"网页&移动应用"
3. 填写应用信息并提交审核
4. 审核通过后获得 `APP_ID`

### 2. 密钥生成规范

#### RSA密钥生成（推荐2048位）
```bash
# 生成应用私钥 (PKCS1格式)
openssl genrsa -out app_private_key.pem 2048

# 从私钥生成公钥
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem

# 转换为PKCS8格式 (支付宝SDK要求)
openssl pkcs8 -topk8 -inform PEM -in app_private_key.pem -outform PEM -nocrypt -out app_private_key_pkcs8.pem
```

#### 在线生成工具
```
https://miniu.alipay.com/keytool/create
```

### 3. 密钥配置格式

#### 应用私钥格式 (PKCS8)
```php
// 去掉头尾和换行符，只保留密钥内容
define('ALIPAY_PRIVATE_KEY', 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDTSuol4ahkH4vMjsRf125I8snzlxr0Bzgn9iMMN+CKVn7v9A1jX7sh5AVKVsmjhZlWGiTLUmSm+fBsmUp/PJ8XKHI1Qv2YW0Nw+WbLWRz2PeufFdv5cCrCC/L8Va5VsNxxPCr6Uflp2fPQETAwd2Hnj52yXIfPJakDJpLSq1yiXoVKY6xjQAbblI934bo/V2COXc7nPirGP9nQndz/uXFxtp3/WQHqcaGXMW4HklOYPwebkggQVxI+lj6BJXgl4ozjMPf7GKS7beJ+KKn8KHziiFC52fHhQcg9yRS6a1UzWfD+UNXUuPoYALzujTeS4OA3wGIYXjlAQutAkKubrhBhAgMBAAECggEBAMoey8XZC6BbnPvdRnT4PAOEXTOrxJjTnyB7ECPL//vVqjAYIgaBuR0+ZuEqDFFkp7353GCzPJ2OUjNQoDb+4qDgi//vSW/JPcaAjZjQcigMK61O19LRPdXKYHKJ8+789KNNjz9N020ylUzgZFy4lutQMuZAJZo+yGK/L4xfaInYbGgaRR7xpsvYS4ODDsjO2LuOXsDhF+t2eSu35G1ksQQCTaKEdjhrGW5Or/9vypwhdAYCspkpMhFj6r2cenWytZDhxn4/Dc1RQCJv1rnqXkL0LCw5lHWp7dOIP3+I5D1etv+xSn+ADCDFRQsY16VHNa/ILl8g5PXX/6l05vh3/pECgYEA6hormzpuGdc6gDzcM3CfY1eB0HmyFFJPNfifaFoALZ+R7WalfJ//6vtL9gRxwrqaOUF3psASUprvu2NhFmcE5YEVOZWWJp28MIgwy+nR4dmTG27BzFzYMxx18SxQLzw0oWnjeNEgOtA0yS/WgtjPxFM/7m3+rA25rUWd5eB3Jf8CgYEA5w6LSGls4LH3u4U/kU+JcqDaapfzUG+JU3S+zVnhlVLjqH3XAtQ278m2Vhgy3iQ0pB1pasFMqEV2Ja46BIkwKLMkpfHX96Nbj5ywCvuD9sKlqoDTUCKg3+aDuDn4DD8qdFqOJxfGXnueQk8lX2xGBLgQ7pjAhmyMx8hkkYGoiZ8CgYAVWpHDtQ65+LHzZJnJb2p6i07iD1e5FtD24VjZEeyWPMn417YcqhOUAZwqMrmVw6OxuVEKutZxBoT7mNMQUhFWRfIDsKtjllvGKYYZ85gbe9c5V18CHy3xa5UujJY72MgMOTZwnNLNypQmDeEJSnGZPObF/u6ODypyrbj7cQAtTQKBgGwvtSu8oUw8SEC3TWJemX7griRG7Zh2ARjgiw5fzW47l3knIuuiLe7sxcFeJ0M4NKW7V8ayp5AKph1SjLve+Hu2FazVpke/Z3nA5fTee5Wo4iUJmOfmrujnaDl0ex5Q3TIJzJGlYi6PNzTZFiit68L95H+zakIGxYwt2lj/GskFAoGABEQsaPaprDK4Qvv8PrTblADWrCI5xJJpzKUHsLPrsBUkDP8gdWnQuAQrk+Ale/yT6GvJYIq+yHGexmSPmYABudzCO8vv+lBiup/Bymj+KrQgRAa3+YLaNKMKRO76fs6hs644c/nq34ln8dthyb5qQkdLhmlDbDWubhi9RBcA/tU=');
```

#### 支付宝公钥格式
```php
// 从支付宝开放平台获取，去掉头尾和换行符
define('ALIPAY_PUBLIC_KEY', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj+1qBI3J9UImYn+nJ8TnkL18xDOnbtEagDUFyalibHxvwKlDn2sFOCI/a78CYmLm6plkeEpNBSBXPry21cwR+IE2/Z4Sjfd0WvDrl0QeEXSOqZO5/6QJ4XDGo5thzTZ6ePcSJv6k3dLaqV0lltrMcrg1vYsEyk5Y2zG77SVBiPz71PhYTQHOmmWOpdWZoq8dlcAaQjK9vmB62Ym6H3pQvXcBgYWj5zmIo1THquAu6t1kPJBqcsrUAjbmNaA5+R+WlOg8VDUE0vbMThJ7H7uoTCDU41fW5KaxZ3UCEaVxTNGkWjGToZE4ufBYjoqKk3m++b7P+F9pmf2vEZqchs8J+wIDAQAB');
```

### 4. 配置验证
```php
/**
 * 验证支付宝配置是否正确
 */
function validateAlipayConfig() {
    $errors = [];
    
    // 检查APP_ID格式
    if (!preg_match('/^\d{16}$/', ALIPAY_APP_ID)) {
        $errors[] = 'APP_ID格式错误，应为16位数字';
    }
    
    // 检查私钥格式
    if (empty(ALIPAY_PRIVATE_KEY)) {
        $errors[] = '应用私钥不能为空';
    }
    
    // 检查公钥格式
    if (empty(ALIPAY_PUBLIC_KEY)) {
        $errors[] = '支付宝公钥不能为空';
    }
    
    // 测试私钥是否可用
    $testData = 'test_signature_data';
    $privateKey = "-----BEGIN PRIVATE KEY-----\n" . 
                  chunk_split(ALIPAY_PRIVATE_KEY, 64, "\n") . 
                  "-----END PRIVATE KEY-----";
    
    if (!openssl_sign($testData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
        $errors[] = '应用私钥格式错误或不可用';
    }
    
    return empty($errors) ? true : $errors;
}
```

---

## 🔧 核心API实现

### 1. 当面付预下单

#### API接口信息
- **接口名称**: alipay.trade.precreate
- **请求方式**: POST
- **接口地址**: https://openapi.alipay.com/gateway.do

#### 核心实现代码
```php
/**
 * 创建当面付订单
 * @param string $outTradeNo 商户订单号
 * @param float $totalAmount 订单金额
 * @param string $subject 订单标题
 * @param string $body 订单描述
 * @return array 返回结果
 */
public function createFaceToFaceOrder($outTradeNo, $totalAmount, $subject, $body = '') {
    // 创建请求对象
    $request = new AlipayTradePrecreateRequest();
    
    // 设置异步通知地址
    $request->setNotifyUrl($this->getNotifyUrl());
    
    // 设置业务参数
    $bizContent = [
        'out_trade_no' => $outTradeNo,                    // 商户订单号
        'total_amount' => number_format($totalAmount, 2, '.', ''), // 订单总金额
        'subject' => $subject,                            // 订单标题
        'body' => $body,                                  // 订单描述
        'timeout_express' => '30m',                       // 订单超时时间
        'store_id' => 'STORE_001'                        // 商户门店编号(可选)
    ];
    
    $request->setBizContent(json_encode($bizContent, JSON_UNESCAPED_UNICODE));
    
    // 执行请求
    $response = $this->aopClient->execute($request);
    
    // 解析响应
    $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
    
    if (!isset($response->$responseNode)) {
        throw new Exception('支付宝API响应格式错误');
    }
    
    $result = $response->$responseNode;
    
    if ($result->code == '10000') {
        // 成功创建订单
        return [
            'success' => true,
            'qr_code' => $result->qr_code,
            'out_trade_no' => $result->out_trade_no,
            'message' => '订单创建成功'
        ];
    } else {
        // 创建订单失败
        $errorMsg = $result->msg ?? '未知错误';
        $subErrorMsg = $result->sub_msg ?? '';
        $fullErrorMsg = $errorMsg . ($subErrorMsg ? ' (' . $subErrorMsg . ')' : '');
        
        throw new Exception("支付宝订单创建失败: $fullErrorMsg (错误码: {$result->code})");
    }
}
```

### 2. 订单查询

#### 核心实现代码
```php
/**
 * 查询订单状态
 * @param string $outTradeNo 商户订单号
 * @return array 查询结果
 */
public function queryOrder($outTradeNo) {
    // 创建查询请求对象
    $request = new AlipayTradeQueryRequest();
    
    // 设置业务参数
    $bizContent = [
        'out_trade_no' => $outTradeNo  // 商户订单号
    ];
    
    $request->setBizContent(json_encode($bizContent));
    
    // 执行请求
    $response = $this->aopClient->execute($request);
    
    // 解析响应
    $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
    $result = $response->$responseNode;
    
    if ($result->code == '10000') {
        // 查询成功
        $tradeStatus = $result->trade_status ?? '';
        $tradeNo = $result->trade_no ?? null;
        $totalAmount = $result->total_amount ?? null;
        
        // 判断是否支付成功
        $isPaid = in_array($tradeStatus, ['TRADE_SUCCESS', 'TRADE_FINISHED']);
        
        return [
            'success' => true,
            'paid' => $isPaid,
            'trade_no' => $tradeNo,
            'trade_status' => $tradeStatus,
            'total_amount' => $totalAmount,
            'out_trade_no' => $outTradeNo
        ];
        
    } else if ($result->code == '40004') {
        // 订单不存在
        return [
            'success' => true,
            'paid' => false,
            'trade_status' => 'NOT_EXIST',
            'out_trade_no' => $outTradeNo
        ];
        
    } else {
        // 查询失败
        $errorMsg = $result->msg ?? '未知错误';
        $subErrorMsg = $result->sub_msg ?? '';
        $fullErrorMsg = $errorMsg . ($subErrorMsg ? ' (' . $subErrorMsg . ')' : '');
        
        throw new Exception("支付宝订单查询失败: $fullErrorMsg (错误码: {$result->code})");
    }
}
```

---

## 📱 二维码生成

### 1. 二维码内容格式
支付宝当面付返回的二维码内容格式：
```
https://qr.alipay.com/bax08431abcdefghijklmnopqrstuvwxyz
```

### 2. 二维码生成实现

#### 使用PHP QR Code库
```bash
# 安装二维码生成库
composer require endroid/qr-code
```

#### 生成二维码图片
```php
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

/**
 * 生成支付二维码
 * @param string $qrContent 二维码内容
 * @param int $size 二维码尺寸
 * @return string 二维码图片的base64编码
 */
public function generatePaymentQrCode($qrContent, $size = 300) {
    try {
        // 创建二维码对象
        $qrCode = new QrCode($qrContent);
        $qrCode->setSize($size);
        $qrCode->setMargin(10);
        
        // 生成PNG格式
        $writer = new PngWriter();
        $result = $writer->write($qrCode);
        
        // 返回base64编码的图片
        return 'data:image/png;base64,' . base64_encode($result->getString());
        
    } catch (Exception $e) {
        throw new Exception('二维码生成失败: ' . $e->getMessage());
    }
}

/**
 * 保存二维码到文件
 * @param string $qrContent 二维码内容
 * @param string $filePath 保存路径
 * @param int $size 二维码尺寸
 * @return bool 是否成功
 */
public function saveQrCodeToFile($qrContent, $filePath, $size = 300) {
    try {
        $qrCode = new QrCode($qrContent);
        $qrCode->setSize($size);
        $qrCode->setMargin(10);
        
        $writer = new PngWriter();
        $result = $writer->write($qrCode);
        
        // 保存到文件
        file_put_contents($filePath, $result->getString());
        
        return true;
        
    } catch (Exception $e) {
        error_log('二维码保存失败: ' . $e->getMessage());
        return false;
    }
}
```

#### 使用原生PHP生成（轻量级方案）
```php
/**
 * 使用Google Charts API生成二维码（需要网络连接）
 * @param string $qrContent 二维码内容
 * @param int $size 二维码尺寸
 * @return string 二维码图片URL
 */
public function generateQrCodeUrl($qrContent, $size = 300) {
    $encodedContent = urlencode($qrContent);
    return "https://chart.googleapis.com/chart?chs={$size}x{$size}&cht=qr&chl={$encodedContent}";
}

/**
 * 下载并保存二维码图片
 * @param string $qrContent 二维码内容
 * @param string $filePath 保存路径
 * @param int $size 二维码尺寸
 * @return bool 是否成功
 */
public function downloadAndSaveQrCode($qrContent, $filePath, $size = 300) {
    $qrUrl = $this->generateQrCodeUrl($qrContent, $size);
    
    $imageData = file_get_contents($qrUrl);
    if ($imageData === false) {
        return false;
    }
    
    return file_put_contents($filePath, $imageData) !== false;
}
```

### 3. 前端展示方案

#### HTML展示
```html
<!-- 直接显示base64图片 -->
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." alt="支付二维码" />

<!-- 显示网络图片 -->
<img src="https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=..." alt="支付二维码" />

<!-- 显示本地保存的图片 -->
<img src="/uploads/qrcodes/order_123456.png" alt="支付二维码" />
```

#### JavaScript动态生成
```javascript
// 使用qrcode.js库
function generateQrCode(content, elementId) {
    const qr = qrcode(0, 'M');
    qr.addData(content);
    qr.make();
    
    document.getElementById(elementId).innerHTML = qr.createImgTag(4);
}

// 调用示例
generateQrCode('https://qr.alipay.com/bax08431...', 'qr-container');
```

---

## 🔐 签名验证机制

### 1. RSA2签名原理

#### 签名流程
1. **参数排序** - 按参数名ASCII码升序排列
2. **构建字符串** - key=value&key=value格式
3. **RSA2签名** - 使用应用私钥对字符串进行SHA256withRSA签名
4. **Base64编码** - 对签名结果进行Base64编码

#### 验签流程
1. **获取签名** - 从响应中提取sign参数
2. **移除签名参数** - 移除sign和sign_type参数
3. **重建字符串** - 按相同规则构建待验证字符串
4. **RSA2验签** - 使用支付宝公钥验证签名

### 2. 签名验证实现

#### 核心验签代码
```php
/**
 * 验证支付宝签名 - 严格按照官方规范
 * @param array $data 待验证数据
 * @return bool 验证结果
 */
public function verifyAlipaySignature($data) {
    // 检查必需的签名参数
    if (!isset($data['sign']) || !isset($data['sign_type'])) {
        error_log('支付宝回调缺少签名信息');
        return false;
    }

    $sign = $data['sign'];
    $signType = $data['sign_type'];

    // 验证签名类型
    if ($signType !== 'RSA2') {
        error_log("不支持的签名类型: $signType");
        return false;
    }

    // 移除签名相关参数
    $dataToVerify = $data;
    unset($dataToVerify['sign'], $dataToVerify['sign_type']);

    // 按照支付宝规范构建待验证字符串
    $stringToBeVerified = $this->buildSignString($dataToVerify);

    // 构建支付宝公钥
    $publicKey = $this->formatPublicKey(ALIPAY_PUBLIC_KEY);

    // 执行RSA2验签
    $result = openssl_verify(
        $stringToBeVerified,
        base64_decode($sign),
        $publicKey,
        OPENSSL_ALGO_SHA256
    );

    if ($result === 1) {
        return true;
    } else if ($result === 0) {
        error_log('支付宝回调签名验证失败: 签名不匹配');
        return false;
    } else {
        $error = openssl_error_string();
        error_log("支付宝回调签名验证错误: $error");
        return false;
    }
}

/**
 * 构建签名字符串
 * @param array $data 数据数组
 * @return string 签名字符串
 */
private function buildSignString($data) {
    // 按照支付宝规范排序和拼接
    ksort($data);
    $stringToBeSigned = '';

    foreach ($data as $key => $value) {
        // 跳过空值和数组
        if ($value !== '' && $value !== null && !is_array($value)) {
            $stringToBeSigned .= $key . '=' . $value . '&';
        }
    }

    // 移除最后的&符号
    return rtrim($stringToBeSigned, '&');
}

/**
 * 格式化公钥
 * @param string $publicKey 原始公钥
 * @return string 格式化后的公钥
 */
private function formatPublicKey($publicKey) {
    return "-----BEGIN PUBLIC KEY-----\n" .
           chunk_split($publicKey, 64, "\n") .
           "-----END PUBLIC KEY-----";
}
```

### 3. 签名调试工具

#### 签名验证调试
```php
/**
 * 调试签名验证过程
 * @param array $data 待验证数据
 * @return array 调试信息
 */
public function debugSignatureVerification($data) {
    $debug = [
        'original_data' => $data,
        'has_sign' => isset($data['sign']),
        'has_sign_type' => isset($data['sign_type']),
        'sign_type' => $data['sign_type'] ?? 'missing',
    ];

    if (isset($data['sign'])) {
        $debug['sign_length'] = strlen($data['sign']);
        $debug['sign_preview'] = substr($data['sign'], 0, 20) . '...';
    }

    // 构建验签字符串
    $dataToVerify = $data;
    unset($dataToVerify['sign'], $dataToVerify['sign_type']);
    $debug['sign_string'] = $this->buildSignString($dataToVerify);
    $debug['sign_string_length'] = strlen($debug['sign_string']);

    // 验证结果
    $debug['verification_result'] = $this->verifyAlipaySignature($data);

    return $debug;
}
```

---

## 📞 异步回调处理

### 1. 回调机制说明

#### 回调触发条件
- 用户完成支付
- 支付状态发生变化
- 订单状态更新

#### 回调重试机制
- 支付宝会重复发送通知
- 间隔时间: 4m, 10m, 10m, 1h, 2h, 6h, 15h
- 总共重试7次

### 2. 回调接口实现

#### 标准回调处理代码
```php
<?php
/**
 * 支付宝异步回调处理
 */

// 引入必要文件
require_once 'config.php';
require_once 'AlipayClient.php';

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo 'fail';
    exit;
}

try {
    // 获取回调数据
    $notifyData = $_POST;

    if (empty($notifyData)) {
        error_log('支付宝回调数据为空');
        echo 'fail';
        exit;
    }

    // 记录原始回调数据
    error_log('支付宝回调原始数据: ' . json_encode($notifyData));

    // 验证必需参数
    $requiredParams = ['out_trade_no', 'trade_no', 'trade_status', 'total_amount'];
    foreach ($requiredParams as $param) {
        if (!isset($notifyData[$param])) {
            error_log("支付宝回调缺少必需参数: $param");
            echo 'fail';
            exit;
        }
    }

    // 初始化支付宝客户端
    $alipayClient = new AlipayClient();

    // 验证签名
    if (!$alipayClient->verifyAlipaySignature($notifyData)) {
        error_log('支付宝回调签名验证失败');
        echo 'fail';
        exit;
    }

    // 处理业务逻辑
    $result = handlePaymentNotify($notifyData);

    if ($result) {
        error_log("支付宝回调处理成功: 订单号 {$notifyData['out_trade_no']}");
        echo 'success';
    } else {
        error_log("支付宝回调处理失败: 订单号 {$notifyData['out_trade_no']}");
        echo 'fail';
    }

} catch (Exception $e) {
    error_log('支付宝回调处理异常: ' . $e->getMessage());
    echo 'fail';
}

/**
 * 处理支付通知业务逻辑
 * @param array $notifyData 回调数据
 * @return bool 处理结果
 */
function handlePaymentNotify($notifyData) {
    $outTradeNo = $notifyData['out_trade_no'];
    $tradeNo = $notifyData['trade_no'];
    $tradeStatus = $notifyData['trade_status'];
    $totalAmount = floatval($notifyData['total_amount']);

    // 查找本地订单
    $order = findOrderByOutTradeNo($outTradeNo);
    if (!$order) {
        error_log("订单不存在: $outTradeNo");
        return false;
    }

    // 验证金额
    if (abs($order['amount'] - $totalAmount) > 0.01) {
        error_log("订单金额不匹配: 本地 {$order['amount']}, 回调 $totalAmount");
        return false;
    }

    // 处理支付成功
    if ($tradeStatus === 'TRADE_SUCCESS' || $tradeStatus === 'TRADE_FINISHED') {
        if ($order['status'] === 'pending') {
            return updateOrderStatus($outTradeNo, 'paid', $tradeNo, $notifyData);
        }
    }

    return true;
}

/**
 * 根据商户订单号查找订单
 * @param string $outTradeNo 商户订单号
 * @return array|null 订单信息
 */
function findOrderByOutTradeNo($outTradeNo) {
    // 这里实现你的订单查询逻辑
    // 返回订单数组或null
    return null;
}

/**
 * 更新订单状态
 * @param string $outTradeNo 商户订单号
 * @param string $status 新状态
 * @param string $tradeNo 支付宝交易号
 * @param array $notifyData 回调数据
 * @return bool 更新结果
 */
function updateOrderStatus($outTradeNo, $status, $tradeNo, $notifyData) {
    // 这里实现你的订单更新逻辑
    // 建议使用数据库事务确保数据一致性
    return true;
}
?>
```

### 3. 回调安全处理

#### IP白名单验证
```php
/**
 * 验证支付宝回调IP
 * @param string $clientIp 客户端IP
 * @return bool 是否为支付宝IP
 */
function verifyAlipayIP($clientIp) {
    // 支付宝回调IP段（需要定期更新）
    $alipayIpRanges = [
        '************/24',
        '************/24',
        '************/24',
        '************/24',
        '************/24'
    ];

    foreach ($alipayIpRanges as $range) {
        if (ipInRange($clientIp, $range)) {
            return true;
        }
    }

    return false;
}

/**
 * 检查IP是否在CIDR范围内
 */
function ipInRange($ip, $cidr) {
    list($subnet, $mask) = explode('/', $cidr);
    $ip = ip2long($ip);
    $subnet = ip2long($subnet);
    $mask = -1 << (32 - $mask);
    $subnet &= $mask;
    return ($ip & $mask) == $subnet;
}
```

#### 防重复处理
```php
/**
 * 防止重复处理同一回调
 * @param string $outTradeNo 订单号
 * @param string $tradeNo 支付宝交易号
 * @return bool 是否已处理
 */
function isNotifyProcessed($outTradeNo, $tradeNo) {
    $cacheKey = "alipay_notify_{$outTradeNo}_{$tradeNo}";

    // 使用Redis或文件缓存
    if (function_exists('redis_get')) {
        return redis_get($cacheKey) !== false;
    }

    // 文件缓存方案
    $cacheFile = "/tmp/{$cacheKey}";
    return file_exists($cacheFile);
}

/**
 * 标记回调已处理
 */
function markNotifyProcessed($outTradeNo, $tradeNo) {
    $cacheKey = "alipay_notify_{$outTradeNo}_{$tradeNo}";

    if (function_exists('redis_set')) {
        redis_set($cacheKey, time(), 3600); // 1小时过期
    } else {
        $cacheFile = "/tmp/{$cacheKey}";
        file_put_contents($cacheFile, time());
    }
}
```

---

## ⚠️ 错误处理规范

### 1. 支付宝API错误码

#### 常见错误码说明
```php
/**
 * 支付宝API错误码映射
 */
const ALIPAY_ERROR_CODES = [
    // 系统错误
    '20000' => '服务不可用',
    '20001' => '授权权限不足',
    '40001' => '缺少必选参数',
    '40002' => '非法的参数',
    '40004' => '业务处理失败',
    '40006' => '权限不足',

    // 业务错误
    'ACQ.TRADE_HAS_SUCCESS' => '交易已被支付',
    'ACQ.TRADE_HAS_CLOSE' => '交易已经关闭',
    'ACQ.BUYER_BALANCE_NOT_ENOUGH' => '买家余额不足',
    'ACQ.PAYMENT_AUTH_CODE_INVALID' => '支付授权码无效',
    'ACQ.CONTEXT_INCONSISTENT' => '交易信息被篡改',

    // 当面付特有错误
    'ACQ.INVALID_STORE_ID' => '商户门店编号无效',
    'ACQ.SELLER_BALANCE_NOT_ENOUGH' => '卖家余额不足',
    'ACQ.REASON_TRADE_BEEN_FREEZEN' => '交易被冻结'
];

/**
 * 获取友好的错误信息
 * @param string $code 错误码
 * @param string $msg 原始错误信息
 * @return string 友好错误信息
 */
function getFriendlyErrorMessage($code, $msg) {
    if (isset(ALIPAY_ERROR_CODES[$code])) {
        return ALIPAY_ERROR_CODES[$code];
    }

    // 根据错误码前缀判断错误类型
    if (strpos($code, 'ACQ.') === 0) {
        return '支付处理失败: ' . $msg;
    } elseif (strpos($code, 'ISV.') === 0) {
        return '商户配置错误: ' . $msg;
    } else {
        return '系统错误: ' . $msg;
    }
}
```

### 2. 异常处理策略

#### 分层异常处理
```php
/**
 * 支付宝异常基类
 */
class AlipayException extends Exception {
    protected $errorCode;
    protected $subCode;
    protected $subMsg;

    public function __construct($message, $code = 0, $subCode = '', $subMsg = '') {
        parent::__construct($message, $code);
        $this->errorCode = $code;
        $this->subCode = $subCode;
        $this->subMsg = $subMsg;
    }

    public function getErrorCode() {
        return $this->errorCode;
    }

    public function getSubCode() {
        return $this->subCode;
    }

    public function getSubMsg() {
        return $this->subMsg;
    }
}

/**
 * 网络异常
 */
class AlipayNetworkException extends AlipayException {}

/**
 * 签名异常
 */
class AlipaySignatureException extends AlipayException {}

/**
 * 业务异常
 */
class AlipayBusinessException extends AlipayException {}
```

#### 统一错误处理
```php
/**
 * 统一处理支付宝API调用
 * @param callable $apiCall API调用函数
 * @return mixed 调用结果
 * @throws AlipayException
 */
function executeAlipayApi($apiCall) {
    try {
        return $apiCall();
    } catch (Exception $e) {
        // 记录详细错误日志
        error_log('支付宝API调用失败: ' . $e->getMessage());
        error_log('错误堆栈: ' . $e->getTraceAsString());

        // 根据错误类型抛出相应异常
        if (strpos($e->getMessage(), 'network') !== false) {
            throw new AlipayNetworkException('网络连接失败，请稍后重试');
        } elseif (strpos($e->getMessage(), 'signature') !== false) {
            throw new AlipaySignatureException('签名验证失败，请检查配置');
        } else {
            throw new AlipayBusinessException('支付处理失败: ' . $e->getMessage());
        }
    }
}

// 使用示例
try {
    $result = executeAlipayApi(function() use ($alipayClient, $orderData) {
        return $alipayClient->createFaceToFaceOrder(
            $orderData['out_trade_no'],
            $orderData['total_amount'],
            $orderData['subject']
        );
    });
} catch (AlipayNetworkException $e) {
    // 网络错误，可以重试
    return ['error' => 'network', 'message' => $e->getMessage()];
} catch (AlipaySignatureException $e) {
    // 配置错误，需要检查配置
    return ['error' => 'config', 'message' => $e->getMessage()];
} catch (AlipayBusinessException $e) {
    // 业务错误，返回给用户
    return ['error' => 'business', 'message' => $e->getMessage()];
}
```

### 3. 日志记录规范

#### 结构化日志
```php
/**
 * 支付宝操作日志记录
 * @param string $action 操作类型
 * @param array $data 相关数据
 * @param string $level 日志级别
 */
function logAlipayOperation($action, $data, $level = 'INFO') {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'level' => $level,
        'action' => $action,
        'client_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'data' => $data
    ];

    // 敏感信息脱敏
    if (isset($logData['data']['sign'])) {
        $logData['data']['sign'] = substr($logData['data']['sign'], 0, 10) . '***';
    }

    $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE);
    error_log($logMessage);
}

// 使用示例
logAlipayOperation('create_order', [
    'out_trade_no' => $outTradeNo,
    'total_amount' => $totalAmount,
    'result' => 'success'
]);

logAlipayOperation('verify_signature', [
    'out_trade_no' => $outTradeNo,
    'verify_result' => false,
    'error' => '签名验证失败'
], 'ERROR');
```

---

## 🔒 安全最佳实践

### 1. 密钥安全管理

#### 密钥存储规范
```php
// ❌ 错误做法：直接在代码中硬编码
define('ALIPAY_PRIVATE_KEY', 'MIIEvQIBADANBgkqhkiG...');

// ✅ 正确做法：从环境变量读取
define('ALIPAY_PRIVATE_KEY', $_ENV['ALIPAY_PRIVATE_KEY'] ?? '');

// ✅ 正确做法：从配置文件读取（配置文件不纳入版本控制）
$config = json_decode(file_get_contents('/etc/alipay/config.json'), true);
define('ALIPAY_PRIVATE_KEY', $config['private_key']);
```

#### 密钥权限控制
```bash
# 设置配置文件权限（仅所有者可读写）
chmod 600 /etc/alipay/config.json
chown www-data:www-data /etc/alipay/config.json

# 设置目录权限
chmod 700 /etc/alipay/
```

### 2. HTTPS强制配置

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 安全头设置
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    location /api/payment/ {
        # 限制回调接口只允许POST
        limit_except POST {
            deny all;
        }

        # 可选：限制支付宝IP访问
        # allow ************/24;
        # deny all;

        fastcgi_pass 127.0.0.1:9000;
        include fastcgi_params;
    }
}
```

### 3. 输入验证与过滤

#### 严格的参数验证
```php
/**
 * 验证订单参数
 * @param array $params 订单参数
 * @return array 验证结果
 */
function validateOrderParams($params) {
    $errors = [];

    // 验证订单号格式
    if (!isset($params['out_trade_no']) || !preg_match('/^[a-zA-Z0-9_-]{6,64}$/', $params['out_trade_no'])) {
        $errors[] = '订单号格式错误';
    }

    // 验证金额
    if (!isset($params['total_amount']) || !is_numeric($params['total_amount'])) {
        $errors[] = '金额格式错误';
    } else {
        $amount = floatval($params['total_amount']);
        if ($amount <= 0 || $amount > 100000) {
            $errors[] = '金额超出允许范围';
        }
        if (round($amount, 2) != $amount) {
            $errors[] = '金额精度错误';
        }
    }

    // 验证订单标题
    if (!isset($params['subject']) || strlen($params['subject']) > 256) {
        $errors[] = '订单标题格式错误';
    }

    // 过滤特殊字符
    $params['subject'] = htmlspecialchars($params['subject'], ENT_QUOTES, 'UTF-8');
    $params['body'] = htmlspecialchars($params['body'] ?? '', ENT_QUOTES, 'UTF-8');

    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'params' => $params
    ];
}
```

### 4. 防重放攻击

#### 时间戳验证
```php
/**
 * 验证请求时间戳
 * @param int $timestamp 请求时间戳
 * @param int $tolerance 允许的时间偏差（秒）
 * @return bool 是否有效
 */
function validateTimestamp($timestamp, $tolerance = 300) {
    $currentTime = time();
    $timeDiff = abs($currentTime - $timestamp);

    return $timeDiff <= $tolerance;
}
```

#### 随机数验证
```php
/**
 * 防重放攻击 - 随机数验证
 * @param string $nonce 随机数
 * @param int $timestamp 时间戳
 * @return bool 是否有效
 */
function validateNonce($nonce, $timestamp) {
    $cacheKey = "nonce_{$nonce}_{$timestamp}";

    // 检查是否已使用
    if (apcu_exists($cacheKey)) {
        return false;
    }

    // 标记为已使用，设置过期时间
    apcu_store($cacheKey, true, 600); // 10分钟过期

    return true;
}
```

---

## 🧪 测试与调试

### 1. 沙箱环境配置

#### 沙箱参数配置
```php
// 沙箱环境配置
const SANDBOX_CONFIG = [
    'gateway_url' => 'https://openapi.alipaydev.com/gateway.do',
    'app_id' => '2021000121671080', // 沙箱应用ID
    'private_key' => 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC6...',
    'alipay_public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuWJKrQ6SWvS9...'
];

// 生产环境配置
const PRODUCTION_CONFIG = [
    'gateway_url' => 'https://openapi.alipay.com/gateway.do',
    'app_id' => 'your_production_app_id',
    'private_key' => 'your_production_private_key',
    'alipay_public_key' => 'alipay_production_public_key'
];

/**
 * 获取环境配置
 * @param bool $isSandbox 是否沙箱环境
 * @return array 配置数组
 */
function getAlipayConfig($isSandbox = false) {
    return $isSandbox ? SANDBOX_CONFIG : PRODUCTION_CONFIG;
}
```

#### 沙箱测试账号
```
买家账号: <EMAIL>
登录密码: 111111
支付密码: 111111

卖家账号: <EMAIL>
登录密码: 111111
```

### 2. 单元测试

#### PHPUnit测试用例
```php
<?php
use PHPUnit\Framework\TestCase;

class AlipayClientTest extends TestCase {
    private $alipayClient;

    protected function setUp(): void {
        // 使用沙箱配置
        $this->alipayClient = new AlipayClient(true);
    }

    /**
     * 测试订单创建
     */
    public function testCreateOrder() {
        $outTradeNo = 'TEST_' . time();
        $totalAmount = 0.01; // 沙箱环境使用小额测试
        $subject = '测试订单';

        $result = $this->alipayClient->createFaceToFaceOrder(
            $outTradeNo,
            $totalAmount,
            $subject
        );

        $this->assertTrue($result['success']);
        $this->assertNotEmpty($result['qr_code']);
        $this->assertEquals($outTradeNo, $result['out_trade_no']);
    }

    /**
     * 测试订单查询
     */
    public function testQueryOrder() {
        // 先创建订单
        $outTradeNo = 'TEST_' . time();
        $createResult = $this->alipayClient->createFaceToFaceOrder(
            $outTradeNo,
            0.01,
            '测试订单'
        );

        $this->assertTrue($createResult['success']);

        // 查询订单
        $queryResult = $this->alipayClient->queryOrder($outTradeNo);

        $this->assertTrue($queryResult['success']);
        $this->assertEquals($outTradeNo, $queryResult['out_trade_no']);
    }

    /**
     * 测试签名验证
     */
    public function testSignatureVerification() {
        // 模拟支付宝回调数据
        $notifyData = [
            'out_trade_no' => 'TEST_123456',
            'trade_no' => '2024121922001234567890',
            'trade_status' => 'TRADE_SUCCESS',
            'total_amount' => '0.01',
            'sign_type' => 'RSA2',
            'sign' => 'mock_signature'
        ];

        // 注意：这里需要使用真实的签名数据进行测试
        // $result = $this->alipayClient->verifyAlipaySignature($notifyData);
        // $this->assertTrue($result);
    }

    /**
     * 测试参数验证
     */
    public function testParameterValidation() {
        // 测试无效金额
        $this->expectException(Exception::class);
        $this->alipayClient->createFaceToFaceOrder('TEST_001', -1, '测试');

        // 测试空订单号
        $this->expectException(Exception::class);
        $this->alipayClient->createFaceToFaceOrder('', 1, '测试');
    }
}
```

### 3. 调试工具

#### 请求响应日志
```php
/**
 * 调试支付宝API请求
 * @param string $method API方法
 * @param array $params 请求参数
 * @param mixed $response 响应结果
 */
function debugAlipayRequest($method, $params, $response) {
    $debugInfo = [
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $method,
        'request_params' => $params,
        'response' => $response,
        'memory_usage' => memory_get_usage(true),
        'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
    ];

    // 脱敏处理
    if (isset($debugInfo['request_params']['sign'])) {
        $debugInfo['request_params']['sign'] = '***HIDDEN***';
    }

    file_put_contents(
        '/tmp/alipay_debug.log',
        json_encode($debugInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n",
        FILE_APPEND
    );
}
```

#### 签名验证调试
```php
/**
 * 调试签名验证过程
 * @param array $data 待验证数据
 */
function debugSignatureProcess($data) {
    echo "=== 签名验证调试 ===\n";
    echo "原始数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";

    // 移除签名参数
    $dataToVerify = $data;
    unset($dataToVerify['sign'], $dataToVerify['sign_type']);

    // 构建签名字符串
    ksort($dataToVerify);
    $signString = '';
    foreach ($dataToVerify as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    $signString = rtrim($signString, '&');

    echo "签名字符串: $signString\n";
    echo "签名字符串长度: " . strlen($signString) . "\n";
    echo "原始签名: " . ($data['sign'] ?? 'missing') . "\n";
    echo "签名长度: " . strlen($data['sign'] ?? '') . "\n";

    // 验证签名
    $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                chunk_split(ALIPAY_PUBLIC_KEY, 64, "\n") .
                "-----END PUBLIC KEY-----";

    $result = openssl_verify(
        $signString,
        base64_decode($data['sign'] ?? ''),
        $publicKey,
        OPENSSL_ALGO_SHA256
    );

    echo "验证结果: " . ($result === 1 ? 'SUCCESS' : 'FAILED') . "\n";
    if ($result !== 1) {
        echo "OpenSSL错误: " . openssl_error_string() . "\n";
    }
    echo "==================\n";
}
```

---

## 🚀 生产环境配置

### 1. 性能优化

#### 连接池配置
```php
/**
 * HTTP连接池配置
 */
class AlipayHttpClient {
    private static $curlHandle = null;

    public static function getHandle() {
        if (self::$curlHandle === null) {
            self::$curlHandle = curl_init();

            // 基础配置
            curl_setopt_array(self::$curlHandle, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2,
                CURLOPT_USERAGENT => 'Alipay-PHP-SDK',

                // 连接复用
                CURLOPT_FORBID_REUSE => false,
                CURLOPT_FRESH_CONNECT => false,

                // HTTP/2支持
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
            ]);
        }

        return self::$curlHandle;
    }

    public static function closeHandle() {
        if (self::$curlHandle !== null) {
            curl_close(self::$curlHandle);
            self::$curlHandle = null;
        }
    }
}
```

#### 缓存策略
```php
/**
 * 支付宝配置缓存
 */
class AlipayConfigCache {
    private static $cache = [];

    public static function get($key) {
        if (!isset(self::$cache[$key])) {
            // 从Redis或其他缓存系统读取
            self::$cache[$key] = apcu_fetch("alipay_config_{$key}");
        }

        return self::$cache[$key];
    }

    public static function set($key, $value, $ttl = 3600) {
        self::$cache[$key] = $value;
        apcu_store("alipay_config_{$key}", $value, $ttl);
    }
}
```

### 2. 监控与告警

#### 健康检查接口
```php
/**
 * 支付宝服务健康检查
 */
function checkAlipayHealth() {
    $health = [
        'status' => 'healthy',
        'checks' => [],
        'timestamp' => time()
    ];

    // 检查配置
    try {
        $configCheck = validateAlipayConfig();
        $health['checks']['config'] = [
            'status' => $configCheck === true ? 'ok' : 'error',
            'message' => $configCheck === true ? '配置正常' : implode(', ', $configCheck)
        ];
    } catch (Exception $e) {
        $health['checks']['config'] = [
            'status' => 'error',
            'message' => $e->getMessage()
        ];
    }

    // 检查网络连接
    try {
        $ch = curl_init('https://openapi.alipay.com/gateway.do');
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $health['checks']['network'] = [
            'status' => $httpCode === 200 ? 'ok' : 'error',
            'message' => $httpCode === 200 ? '网络连接正常' : "HTTP状态码: $httpCode"
        ];
    } catch (Exception $e) {
        $health['checks']['network'] = [
            'status' => 'error',
            'message' => '网络连接失败: ' . $e->getMessage()
        ];
    }

    // 检查磁盘空间
    $freeSpace = disk_free_space('/tmp');
    $totalSpace = disk_total_space('/tmp');
    $usagePercent = (1 - $freeSpace / $totalSpace) * 100;

    $health['checks']['disk'] = [
        'status' => $usagePercent < 90 ? 'ok' : 'warning',
        'message' => sprintf('磁盘使用率: %.1f%%', $usagePercent)
    ];

    // 总体状态
    foreach ($health['checks'] as $check) {
        if ($check['status'] === 'error') {
            $health['status'] = 'unhealthy';
            break;
        } elseif ($check['status'] === 'warning' && $health['status'] === 'healthy') {
            $health['status'] = 'degraded';
        }
    }

    return $health;
}
```

### 3. 日志轮转

#### Logrotate配置
```bash
# /etc/logrotate.d/alipay
/var/log/alipay/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/bin/systemctl reload php7.4-fpm > /dev/null 2>&1 || true
    endscript
}
```

---

## ❓ 常见问题解决

### 1. 签名验证失败

#### 问题排查步骤
```php
/**
 * 签名问题诊断工具
 */
function diagnoseSignatureIssue($notifyData) {
    $diagnosis = [];

    // 1. 检查必需参数
    $requiredParams = ['sign', 'sign_type'];
    foreach ($requiredParams as $param) {
        if (!isset($notifyData[$param])) {
            $diagnosis[] = "缺少必需参数: $param";
        }
    }

    // 2. 检查签名类型
    if (isset($notifyData['sign_type']) && $notifyData['sign_type'] !== 'RSA2') {
        $diagnosis[] = "签名类型错误: {$notifyData['sign_type']}，应为RSA2";
    }

    // 3. 检查公钥格式
    if (empty(ALIPAY_PUBLIC_KEY)) {
        $diagnosis[] = "支付宝公钥未配置";
    } elseif (strlen(ALIPAY_PUBLIC_KEY) < 200) {
        $diagnosis[] = "支付宝公钥长度异常";
    }

    // 4. 检查签名字符串构建
    $dataToVerify = $notifyData;
    unset($dataToVerify['sign'], $dataToVerify['sign_type']);

    ksort($dataToVerify);
    $signString = '';
    foreach ($dataToVerify as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    $signString = rtrim($signString, '&');

    if (empty($signString)) {
        $diagnosis[] = "签名字符串为空";
    }

    // 5. 测试公钥格式
    $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                chunk_split(ALIPAY_PUBLIC_KEY, 64, "\n") .
                "-----END PUBLIC KEY-----";

    $testKey = openssl_pkey_get_public($publicKey);
    if ($testKey === false) {
        $diagnosis[] = "公钥格式错误: " . openssl_error_string();
    } else {
        openssl_pkey_free($testKey);
    }

    return $diagnosis;
}
```

### 2. 网络连接问题

#### 连接测试工具
```php
/**
 * 测试支付宝API连接
 */
function testAlipayConnection() {
    $gateways = [
        'production' => 'https://openapi.alipay.com/gateway.do',
        'sandbox' => 'https://openapi.alipaydev.com/gateway.do'
    ];

    $results = [];

    foreach ($gateways as $env => $url) {
        $start = microtime(true);

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_NOBODY => true
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $responseTime = microtime(true) - $start;

        curl_close($ch);

        $results[$env] = [
            'url' => $url,
            'http_code' => $httpCode,
            'response_time' => round($responseTime * 1000, 2) . 'ms',
            'success' => $httpCode === 200,
            'error' => $error
        ];
    }

    return $results;
}
```

### 3. 订单状态同步问题

#### 状态同步检查
```php
/**
 * 检查订单状态同步
 * @param string $outTradeNo 商户订单号
 * @return array 同步状态
 */
function checkOrderSync($outTradeNo) {
    $alipayClient = new AlipayClient();

    try {
        // 查询支付宝订单状态
        $alipayResult = $alipayClient->queryOrder($outTradeNo);

        // 查询本地订单状态
        $localOrder = findOrderByOutTradeNo($outTradeNo);

        $sync = [
            'out_trade_no' => $outTradeNo,
            'alipay_status' => $alipayResult['trade_status'] ?? 'UNKNOWN',
            'local_status' => $localOrder['status'] ?? 'NOT_FOUND',
            'is_synced' => false,
            'suggestion' => ''
        ];

        // 状态映射
        $statusMap = [
            'TRADE_SUCCESS' => 'paid',
            'TRADE_FINISHED' => 'paid',
            'TRADE_CLOSED' => 'closed',
            'WAIT_BUYER_PAY' => 'pending'
        ];

        $expectedLocalStatus = $statusMap[$sync['alipay_status']] ?? 'unknown';
        $sync['is_synced'] = $sync['local_status'] === $expectedLocalStatus;

        if (!$sync['is_synced']) {
            if ($sync['alipay_status'] === 'TRADE_SUCCESS' && $sync['local_status'] === 'pending') {
                $sync['suggestion'] = '支付宝显示已支付，但本地状态未更新，建议检查回调处理';
            } elseif ($sync['local_status'] === 'NOT_FOUND') {
                $sync['suggestion'] = '本地订单不存在，请检查订单号';
            } else {
                $sync['suggestion'] = '状态不一致，建议手动核对';
            }
        }

        return $sync;

    } catch (Exception $e) {
        return [
            'out_trade_no' => $outTradeNo,
            'error' => $e->getMessage(),
            'suggestion' => '查询失败，请检查网络连接和配置'
        ];
    }
}
```

---

## 📚 附录

### 1. 官方文档链接
- [支付宝开放平台](https://open.alipay.com/)
- [当面付API文档](https://opendocs.alipay.com/apis/api_1/alipay.trade.precreate)
- [异步通知说明](https://opendocs.alipay.com/open/270/105902)
- [签名验证说明](https://opendocs.alipay.com/open/291/105974)
- [错误码说明](https://opendocs.alipay.com/open/common/103259)

### 2. 开发工具
- [密钥生成工具](https://miniu.alipay.com/keytool/create)
- [签名验证工具](https://miniu.alipay.com/keytool/verify)
- [沙箱环境](https://openhome.alipay.com/platform/appDaily.htm)

### 3. SDK下载
- [PHP SDK](https://opendocs.alipay.com/open/54/cyz7do)
- [Java SDK](https://opendocs.alipay.com/open/54/103419)
- [.NET SDK](https://opendocs.alipay.com/open/54/104509)

---

**文档版本**: v1.0.0
**最后更新**: 2024-12-19
**维护者**: 开发团队

> 💡 **重要提示**:
> 1. 生产环境必须使用HTTPS
> 2. 定期更新支付宝公钥
> 3. 妥善保管应用私钥
> 4. 及时处理异步回调
> 5. 做好日志记录和监控
